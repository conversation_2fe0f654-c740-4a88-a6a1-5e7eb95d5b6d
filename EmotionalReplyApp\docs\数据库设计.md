# 情感回复助手 - 数据库设计文档

## 数据库概述

### 技术选型
- **数据库**: MySQL 8.0+
- **连接池**: HikariCP
- **ORM框架**: MyBatis Plus
- **字符集**: utf8mb4 (支持emoji表情)
- **存储引擎**: InnoDB

### 设计原则
- **数据一致性**: 使用外键约束保证数据完整性
- **性能优化**: 合理设计索引，支持高并发查询
- **扩展性**: 预留字段，支持功能扩展
- **数据安全**: 敏感信息加密存储

## 数据表设计

### 1. 用户表 (users)
存储用户基本信息、角色权限和会员信息

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键ID |
| user_id | VARCHAR | 64 | UNIQUE, NOT NULL | 用户唯一标识 |
| username | VARCHAR | 50 | UNIQUE, NOT NULL | 用户名 |
| email | VARCHAR | 100 | UNIQUE | 邮箱地址 |
| password_hash | VARCHAR | 255 | NOT NULL | 密码哈希 |
| nickname | VARCHAR | 50 | - | 用户昵称 |
| avatar_url | VARCHAR | 255 | - | 头像URL |
| role | VARCHAR | 20 | DEFAULT 'free' | 用户角色(free:免费用户,premium:高级会员,admin:管理员) |
| premium_expire | DATETIME | - | - | 高级会员到期时间 |
| daily_quota | INT | - | DEFAULT 10 | 每日使用配额 |
| used_today | INT | - | DEFAULT 0 | 今日已使用次数 |
| total_used | BIGINT | - | DEFAULT 0 | 总使用次数 |
| last_used_date | DATE | - | - | 最后使用日期 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| status | TINYINT | - | DEFAULT 1 | 状态(0:禁用,1:正常,2:冻结) |

### 2. 用户偏好设置表 (user_preferences)
存储用户个性化设置

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键ID |
| user_id | VARCHAR | 64 | UNIQUE, NOT NULL, FK | 用户ID |
| preferred_styles | JSON | - | - | 偏好的回复风格 |
| reply_length_preference | TINYINT | - | DEFAULT 2 | 回复长度偏好(1:简短,2:中等,3:详细) |
| emotion_sensitivity | DECIMAL | 3,2 | DEFAULT 0.5 | 情感敏感度 |
| auto_copy | TINYINT | - | DEFAULT 1 | 是否自动复制 |
| bubble_position | JSON | - | - | 气泡位置偏好 |
| theme_color | VARCHAR | 10 | DEFAULT '#2196F3' | 主题色 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 3. 使用统计表 (usage_statistics)
存储用户使用统计和配额管理

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键ID |
| user_id | VARCHAR | 64 | NOT NULL | 用户ID |
| date | DATE | - | NOT NULL | 统计日期 |
| daily_used_count | INT | - | DEFAULT 0 | 当日使用次数 |
| daily_quota | INT | - | DEFAULT 10 | 当日配额 |
| quota_exceeded_count | INT | - | DEFAULT 0 | 超额尝试次数 |
| most_used_style | VARCHAR | 20 | - | 最常用风格 |
| avg_response_time | INT | - | DEFAULT 0 | 平均响应时间(毫秒) |
| success_rate | DECIMAL | 5,2 | DEFAULT 100.00 | 成功率 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计:**
- `uk_user_date (user_id, date)` - 唯一约束
- `idx_date (date)` - 日期查询优化

## 数据字典

### 用户角色 (role)
- `free` - 免费用户 (每日10次)
- `premium` - 高级会员 (每日150次)
- `admin` - 管理员 (无限制)

### 用户状态 (status)
- `0` - 禁用
- `1` - 正常
- `2` - 冻结

### 回复风格 (reply_style)
- `warm_caring` - 温暖关怀型
- `humorous` - 幽默风趣型
- `rational` - 理性分析型
- `concise` - 简洁直接型
- `romantic` - 浪漫情话型

### 配额规则
| 角色类型 | 每日配额 | 月费 | 超额处理 |
|----------|----------|------|----------|
| free | 10次 | 免费 | 提示升级 |
| premium | 150次 | ¥9.9 | 软限制提醒 |
| admin | 无限制 | - | - |

## 性能优化

### 索引策略
1. **主键索引**: 所有表都有自增主键
2. **唯一索引**: 用户ID等唯一字段
3. **复合索引**: 多字段查询优化
4. **时间索引**: 支持时间范围查询

### 分区策略
```sql
-- 按月分区消息记录表
ALTER TABLE message_records PARTITION BY RANGE (YEAR(created_time)*100 + MONTH(created_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- 继续添加分区...
);
```

### 数据清理策略
```sql
-- 定期清理90天前的消息记录
DELETE FROM message_records 
WHERE created_time < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 定期清理未被选择的回复记录
DELETE FROM reply_records 
WHERE is_selected = 0 AND created_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## 备份和恢复

### 备份策略
- **全量备份**: 每日凌晨进行全量备份
- **增量备份**: 每小时进行增量备份
- **binlog备份**: 实时备份binlog文件

### 恢复策略
- **时间点恢复**: 基于binlog进行时间点恢复
- **表级恢复**: 针对特定表的数据恢复
- **灾难恢复**: 主从切换和数据中心切换
