# 情感回复助手 - 界面设计说明

## 设计理念

### 核心原则
- **非侵入性**: 悬浮气泡设计，不干扰用户正常聊天
- **快速响应**: 一键操作，3秒内完成回复生成
- **简洁美观**: Material Design风格，符合Android设计规范
- **智能适应**: 根据使用场景自动调整显示状态

## 界面层级结构

```
应用启动
├── 权限引导页面
├── 主界面 (MainActivity)
│   ├── 功能介绍
│   ├── 设置入口
│   └── 启动悬浮服务
└── 悬浮模式
    ├── 气泡状态 (最小化)
    └── 弹出对话框 (激活状态)
        ├── 消息输入区
        ├── 回复生成区
        └── 快速操作区
```

## 详细界面设计

### 1. 悬浮气泡 (FloatingBubble)

#### 视觉设计
- **尺寸**: 56dp x 56dp (Material Design标准FAB尺寸)
- **形状**: 圆形，带有轻微阴影
- **颜色**: 主题色渐变 (#4CAF50 到 #2196F3)
- **图标**: 简洁的对话气泡图标
- **透明度**: 80% (不使用时) / 100% (激活时)

#### 交互状态
```
默认状态: 半透明，显示app图标
悬停状态: 完全不透明，轻微放大 (1.1倍)
拖拽状态: 跟随手指移动，显示删除区域
激活状态: 弹出对话框，气泡保持高亮
```

#### 位置逻辑
- 默认位置：屏幕右侧中央
- 自动吸附：拖拽释放后自动贴边
- 智能避让：避开状态栏、导航栏、刘海屏区域
- 记忆位置：记住用户最后放置的位置

### 2. 快速回复对话框 (QuickReplyDialog)

#### 布局结构
```
┌─────────────────────────────┐
│  📋 粘贴消息                │ ← 顶部操作栏
├─────────────────────────────┤
│  [输入框] "粘贴或输入消息"   │ ← 消息输入区
├─────────────────────────────┤
│  🧠 正在分析情感...         │ ← 分析状态
├─────────────────────────────┤
│  💬 温暖关怀型              │ ← 回复选项1
│  "听起来你今天过得不容易..."  │
│  [复制] [编辑]              │
├─────────────────────────────┤
│  😄 幽默风趣型              │ ← 回复选项2
│  "哈哈，这个我懂..."        │
│  [复制] [编辑]              │
├─────────────────────────────┤
│  🤔 理性分析型              │ ← 回复选项3
│  "从你的描述来看..."        │
│  [复制] [编辑]              │
└─────────────────────────────┘
```

#### 尺寸规格
- **宽度**: 280dp (适合单手操作)
- **最大高度**: 400dp (避免遮挡过多内容)
- **圆角**: 16dp
- **边距**: 16dp (距离屏幕边缘)

#### 动画效果
- **弹出动画**: 从气泡位置缩放弹出 (300ms)
- **收起动画**: 缩放回气泡位置 (200ms)
- **内容加载**: 回复选项逐个淡入 (100ms间隔)

### 3. 主应用界面 (MainActivity)

#### 功能区域
1. **欢迎区域**
   - App Logo和名称
   - 功能简介动画
   - 开始使用按钮

2. **权限设置区域**
   - 悬浮窗权限状态
   - 一键开启按钮
   - 权限说明

3. **功能设置区域**
   - 回复风格偏好
   - 智能显示设置
   - 历史记录管理

4. **底部导航**
   - 首页、历史、设置

## 颜色方案

### 主色调
- **主色**: #2196F3 (蓝色 - 智能、可靠)
- **辅助色**: #4CAF50 (绿色 - 友好、积极)
- **强调色**: #FF9800 (橙色 - 活力、创新)

### 功能色彩
- **成功**: #4CAF50
- **警告**: #FF9800  
- **错误**: #F44336
- **信息**: #2196F3

### 文字颜色
- **主要文字**: #212121 (87% 不透明度)
- **次要文字**: #757575 (54% 不透明度)
- **提示文字**: #9E9E9E (38% 不透明度)

## 图标设计

### 应用图标
- 圆形背景，渐变色
- 中央为简化的对话气泡
- 内含智能符号 (如小闪电或齿轮)

### 功能图标
- 消息: 💬 (对话气泡)
- 分析: 🧠 (大脑)
- 复制: 📋 (剪贴板)
- 设置: ⚙️ (齿轮)
- 历史: 📚 (书本)

## 响应式设计

### 屏幕适配
- **小屏幕** (<5英寸): 对话框宽度240dp
- **中等屏幕** (5-6英寸): 对话框宽度280dp  
- **大屏幕** (>6英寸): 对话框宽度320dp

### 横屏适配
- 气泡位置自动调整
- 对话框布局改为横向排列
- 减少垂直空间占用

## 无障碍设计

### 可访问性
- 所有交互元素支持TalkBack
- 最小触摸目标44dp
- 高对比度模式支持
- 字体大小跟随系统设置

### 手势支持
- 双击气泡：快速粘贴并生成
- 长按气泡：显示更多选项
- 滑动对话框：切换回复风格

## 性能优化

### 渲染优化
- 使用硬件加速
- 避免过度绘制
- 合理使用动画
- 图片资源压缩

### 内存管理
- 及时释放不用的视图
- 使用对象池复用
- 避免内存泄漏
