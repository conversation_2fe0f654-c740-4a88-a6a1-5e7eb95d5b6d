# 悬浮气泡技术实现指南

## 核心技术栈

### Android 悬浮窗技术
- **WindowManager**: 管理悬浮窗的显示和布局
- **TYPE_APPLICATION_OVERLAY**: Android 8.0+ 推荐的悬浮窗类型
- **前台服务**: 保持悬浮窗常驻，避免被系统杀死

### 权限管理
- **SYSTEM_ALERT_WINDOW**: 悬浮窗显示权限
- **FOREGROUND_SERVICE**: 前台服务权限
- **动态权限申请**: 引导用户开启悬浮窗权限

## 关键实现步骤

### 1. 权限申请和检查

```java
public class PermissionManager {
    private static final int REQUEST_OVERLAY_PERMISSION = 1001;
    
    // 检查悬浮窗权限
    public static boolean hasOverlayPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(context);
        }
        return true;
    }
    
    // 申请悬浮窗权限
    public static void requestOverlayPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            activity.startActivityForResult(intent, REQUEST_OVERLAY_PERMISSION);
        }
    }
}
```

### 2. 悬浮气泡服务

```java
public class FloatingBubbleService extends Service {
    private WindowManager windowManager;
    private View bubbleView;
    private WindowManager.LayoutParams bubbleParams;
    
    @Override
    public void onCreate() {
        super.onCreate();
        createFloatingBubble();
        startForeground(NOTIFICATION_ID, createNotification());
    }
    
    private void createFloatingBubble() {
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        
        // 创建气泡视图
        bubbleView = LayoutInflater.from(this)
            .inflate(R.layout.floating_bubble, null);
        
        // 设置悬浮窗参数
        bubbleParams = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ?
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY :
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            PixelFormat.TRANSLUCENT
        );
        
        // 设置初始位置
        bubbleParams.gravity = Gravity.TOP | Gravity.START;
        bubbleParams.x = 0;
        bubbleParams.y = 100;
        
        // 添加到窗口管理器
        windowManager.addView(bubbleView, bubbleParams);
        
        // 设置触摸监听
        setupTouchListener();
    }
}
```

### 3. 触摸事件处理

```java
private void setupTouchListener() {
    bubbleView.setOnTouchListener(new View.OnTouchListener() {
        private int initialX, initialY;
        private float initialTouchX, initialTouchY;
        private boolean isDragging = false;
        private long touchStartTime;
        
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    touchStartTime = System.currentTimeMillis();
                    initialX = bubbleParams.x;
                    initialY = bubbleParams.y;
                    initialTouchX = event.getRawX();
                    initialTouchY = event.getRawY();
                    isDragging = false;
                    return true;
                    
                case MotionEvent.ACTION_MOVE:
                    float deltaX = event.getRawX() - initialTouchX;
                    float deltaY = event.getRawY() - initialTouchY;
                    
                    if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
                        isDragging = true;
                        bubbleParams.x = initialX + (int) deltaX;
                        bubbleParams.y = initialY + (int) deltaY;
                        windowManager.updateViewLayout(bubbleView, bubbleParams);
                    }
                    return true;
                    
                case MotionEvent.ACTION_UP:
                    long touchDuration = System.currentTimeMillis() - touchStartTime;
                    
                    if (!isDragging && touchDuration < 200) {
                        // 单击事件 - 显示回复对话框
                        showReplyDialog();
                    } else if (isDragging) {
                        // 拖拽结束 - 自动贴边
                        autoSnapToEdge();
                    }
                    return true;
            }
            return false;
        }
    });
}
```

### 4. 自动贴边功能

```java
private void autoSnapToEdge() {
    DisplayMetrics displayMetrics = new DisplayMetrics();
    windowManager.getDefaultDisplay().getMetrics(displayMetrics);
    int screenWidth = displayMetrics.widthPixels;
    
    // 判断气泡应该贴向哪一边
    int centerX = bubbleParams.x + bubbleView.getWidth() / 2;
    int targetX = centerX < screenWidth / 2 ? 0 : screenWidth - bubbleView.getWidth();
    
    // 使用动画移动到目标位置
    ValueAnimator animator = ValueAnimator.ofInt(bubbleParams.x, targetX);
    animator.setDuration(300);
    animator.setInterpolator(new DecelerateInterpolator());
    animator.addUpdateListener(animation -> {
        bubbleParams.x = (int) animation.getAnimatedValue();
        windowManager.updateViewLayout(bubbleView, bubbleParams);
    });
    animator.start();
}
```

### 5. 回复对话框显示

```java
private void showReplyDialog() {
    Intent intent = new Intent(this, ReplyDialogActivity.class);
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
    intent.putExtra("bubble_x", bubbleParams.x);
    intent.putExtra("bubble_y", bubbleParams.y);
    startActivity(intent);
}
```

## 性能优化策略

### 1. 内存优化
```java
public class FloatingBubbleService extends Service {
    private static final int MAX_IDLE_TIME = 5 * 60 * 1000; // 5分钟
    private Handler idleHandler = new Handler();
    private Runnable idleRunnable;
    
    private void startIdleTimer() {
        idleRunnable = () -> {
            // 进入省电模式
            enterPowerSaveMode();
        };
        idleHandler.postDelayed(idleRunnable, MAX_IDLE_TIME);
    }
    
    private void enterPowerSaveMode() {
        // 减少动画
        // 降低刷新率
        // 释放不必要的资源
    }
}
```

### 2. 电池优化
```java
// 在AndroidManifest.xml中添加
<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

// 在代码中请求电池优化白名单
private void requestBatteryOptimization() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
        if (!pm.isIgnoringBatteryOptimizations(getPackageName())) {
            Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + getPackageName()));
            startActivity(intent);
        }
    }
}
```

## 兼容性处理

### Android版本适配
```java
public class CompatibilityUtils {
    public static int getOverlayType() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            return WindowManager.LayoutParams.TYPE_PHONE;
        }
    }
    
    public static boolean needsOverlayPermission() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M;
    }
}
```

### 屏幕适配
```java
private void adaptToScreen() {
    DisplayMetrics metrics = getResources().getDisplayMetrics();
    float density = metrics.density;
    
    // 根据屏幕密度调整气泡大小
    int bubbleSize = (int) (56 * density); // 56dp
    
    ViewGroup.LayoutParams params = bubbleView.getLayoutParams();
    params.width = bubbleSize;
    params.height = bubbleSize;
    bubbleView.setLayoutParams(params);
}
```

## 调试和测试

### 日志记录
```java
public class FloatingLogger {
    private static final String TAG = "FloatingBubble";
    
    public static void logBubbleEvent(String event, Object... args) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, String.format(event, args));
        }
    }
}
```

### 性能监控
```java
private void monitorPerformance() {
    // 监控内存使用
    Runtime runtime = Runtime.getRuntime();
    long usedMemory = runtime.totalMemory() - runtime.freeMemory();
    
    // 监控CPU使用
    // 监控电池消耗
    
    FloatingLogger.logBubbleEvent("Memory usage: %d KB", usedMemory / 1024);
}
