# 情感回复助手 App

## 项目简介

这是一个基于Java和Android开发的情感智能应用，帮助用户生成合适的回复消息。

### 核心功能
- 📝 消息粘贴和分析
- 🧠 智能情感识别
- 💬 多风格回复生成
- 📋 一键复制回复
- 📚 历史记录管理

### 使用流程
1. 复制收到的消息
2. 在app中粘贴
3. 系统分析并生成回复建议
4. 选择合适的回复
5. 一键复制使用

## 项目结构

```
EmotionalReplyApp/
├── android-app/         # Android应用主体
├── backend-service/     # 后端服务(可选)
├── docs/               # 项目文档
├── 项目设计文档.md      # 详细设计文档
└── README.md           # 项目说明
```

## 开发环境

### Android开发
- Android Studio Arctic Fox或更高版本
- JDK 8或更高版本
- Android SDK API 21+

### 后端开发(可选)
- IntelliJ IDEA或Eclipse
- Java 8+
- Spring Boot 2.x
- Maven 3.x

## 快速开始

1. 克隆项目到本地
2. 使用Android Studio打开`android-app`目录
3. 同步Gradle依赖
4. 运行项目

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue。
